// Core type definitions for the IVD News Aggregation system

export interface User {
  id: string
  email?: string
  phone?: string
  authProvider: 'email' | 'phone' | 'wechat' | 'qq' | 'ciam'
  ciamUserId?: string
  profile: UserProfile
  subscriptionPreferences: SubscriptionPreferences
  createdAt: Date
  updatedAt: Date
}

export interface UserProfile {
  name?: string
  company?: string
  position?: string
  interests: string[]
}

export interface SubscriptionPreferences {
  categories: string[]
  frequency: 'realtime' | 'daily' | 'weekly'
  notificationMethods: ('email' | 'push' | 'sms')[]
}

export interface NewsSource {
  id: string
  name: string
  category: 'regulatory' | 'industry' | 'news' | 'market'
  baseUrl: string
  apiConfig: Record<string, any>
  updateFrequency: number
  contentTypes: string[]
  geoFocus: string[]
  isActive: boolean
  createdAt: Date
}

export interface Article {
  id: string
  sourceId: string
  title: string
  content: string
  summary: string
  author?: string
  publishedAt: Date
  tags: string[]
  category: string
  url: string
  createdAt: Date
}

export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  billingCycle: 'monthly' | 'yearly'
  features: string[]
  isActive: boolean
}

export interface UserSubscription {
  id: string
  userId: string
  planId: string
  status: 'active' | 'cancelled' | 'expired'
  currentPeriodStart: Date
  currentPeriodEnd: Date
  paymentMethod: 'wechat' | 'alipay'
  createdAt: Date
}

export interface Payment {
  id: string
  subscriptionId: string
  amount: number
  currency: string
  paymentMethod: 'wechat' | 'alipay'
  transactionId: string
  status: 'pending' | 'completed' | 'failed'
  createdAt: Date
}

export interface NotificationPreferences {
  types: string[]
  frequency: 'immediate' | 'daily' | 'weekly'
  deliveryMethods: ('email' | 'push' | 'sms')[]
}

export interface SearchFilters {
  categories?: string[]
  sources?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  tags?: string[]
}

export interface APIResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}